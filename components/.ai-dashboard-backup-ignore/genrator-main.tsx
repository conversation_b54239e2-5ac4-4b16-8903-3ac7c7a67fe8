"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Loader2, Send, AlertCircle, CheckCircle, Download, Eye, Coins, Zap, Image, Video, MessageSquare } from "lucide-react";
import { toast } from "sonner";

interface AIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  credits_per_unit: number;
  unit_type: string;
  description?: string;
  supported_features?: string[];
}

interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

interface AINonFullMainProps {
  modelType?: string;
  onResultChange?: (result: GenerationResult | null) => void;
  onGeneratingChange?: (isGenerating: boolean) => void;
}

const MODEL_TYPE_ICONS = {
  text: MessageSquare,
  image: Image,
  video: Video,
  multimodal: Zap
};

const MODEL_TYPE_LABELS = {
  text: "文本生成",
  image: "图像生成",
  video: "视频生成",
  multimodal: "多模态"
};

export function InputMain({
  modelType,
  onResultChange,
  onGeneratingChange
}: AINonFullMainProps = {}) {
  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);
  const [models, setModels] = useState<AIModel[]>([]);
  const [groupedModels, setGroupedModels] = useState<Record<string, AIModel[]>>({});
  const [modelsLoading, setModelsLoading] = useState(true);
  const [modelsError, setModelsError] = useState<string | null>(null);
  const [prompt, setPrompt] = useState("");
  const [options, setOptions] = useState<any>({
    size: '1:1', // 默认图像尺寸
    aspectRatio: '1:1', // 默认宽高比
    variants: 1, // 默认生成数量
    temperature: 0.7, // 默认创造性
    max_tokens: 1000, // 默认最大输出长度
    cdn: 'global' // 默认CDN区域
  });
  const [loading, setLoading] = useState(false);
  const [costEstimate, setCostEstimate] = useState<any>(null);
  const [userCredits, setUserCredits] = useState<number>(0);

  useEffect(() => {
    fetchUserCredits();
  }, []);

  useEffect(() => {
    fetchModels();
  }, [modelType]);

  useEffect(() => {
    if (selectedModel && prompt) {
      estimateCost();
    }
  }, [selectedModel, prompt, options]);

  // 当模型切换时，重置相关选项的默认值
  useEffect(() => {
    if (selectedModel) {
      const newOptions = { ...options };

      // 根据模型类型设置默认值
      if (selectedModel.model_type === 'image') {
        if (!newOptions.size && !newOptions.aspectRatio) {
          newOptions.size = '1:1';
          newOptions.aspectRatio = '1:1';
        }
        if (!newOptions.variants) {
          newOptions.variants = 1;
        }
      } else if (selectedModel.model_type === 'text' || selectedModel.model_type === 'multimodal') {
        if (!newOptions.max_tokens) {
          newOptions.max_tokens = 1000;
        }
        if (!newOptions.temperature) {
          newOptions.temperature = 0.7;
        }
      }

      if (!newOptions.cdn) {
        newOptions.cdn = 'global';
      }

      setOptions(newOptions);
    }
  }, [selectedModel]);

  const fetchModels = async () => {
    try {
      setModelsLoading(true);
      setModelsError(null);

      const url = modelType
        ? `/api/ai/models?type=${modelType}`
        : '/api/ai/models';

      const response = await fetch(url);
      const data = await response.json();

      if (data.code === 0) {
        const allModels = data.data.models;
        const filteredModels = modelType
          ? allModels.filter((model: AIModel) => model.model_type === modelType)
          : allModels;

        setModels(filteredModels);

        // 重新分组过滤后的模型
        const grouped = filteredModels.reduce((acc: Record<string, AIModel[]>, model: AIModel) => {
          if (!acc[model.model_type]) {
            acc[model.model_type] = [];
          }
          acc[model.model_type].push(model);
          return acc;
        }, {});

        setGroupedModels(grouped);
      } else {
        setModelsError(data.msg || 'Failed to fetch models');
      }
    } catch (err) {
      setModelsError('Network error');
    } finally {
      setModelsLoading(false);
    }
  };

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/get-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      if (data.code === 0) {
        setUserCredits(data.data.credits?.left_credits || 0);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };

  const estimateCost = async () => {
    if (!selectedModel || !prompt) return;

    try {
      const response = await fetch('/api/ai/estimate-cost', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });
      const data = await response.json();
      if (data.code === 0) {
        setCostEstimate(data.data);
      }
    } catch (error) {
      console.error('Failed to estimate cost:', error);
    }
  };

  const handleGenerate = async () => {
    if (!selectedModel || !prompt.trim()) {
      toast.error("请选择模型并输入提示词");
      return;
    }

    if (costEstimate && !costEstimate.user_credits.can_afford) {
      toast.error("积分不足，请充值后再试");
      return;
    }

    setLoading(true);
    onGeneratingChange?.(true);
    onResultChange?.(null);

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });

      const data = await response.json();

      if (data.code === 0) {
        // 如果是异步任务，开始轮询结果，不立即设置结果
        if (data.data.status === 'pending' || data.data.status === 'running') {
          onResultChange?.(data.data); // 设置初始状态
          pollResult(data.data.id);
        } else {
          // 同步任务，直接设置结果
          onResultChange?.(data.data);
          toast.success("生成完成！");
          fetchUserCredits(); // 刷新积分余额
          onGeneratingChange?.(false);
        }
      } else {
        toast.error(data.msg || "生成失败");
        onGeneratingChange?.(false);
      }
    } catch (error) {
      toast.error("网络错误，请重试");
      onGeneratingChange?.(false);
    } finally {
      setLoading(false);
    }
  };

  const pollResult = async (requestId: string) => {
    const maxAttempts = 60; // 最多轮询60次（5分钟）
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch('/api/ai/result', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ request_id: requestId })
        });

        const data = await response.json();
        
        if (data.code === 0) {
          onResultChange?.(data.data);

          if (data.data.status === 'success') {
            toast.success("生成完成！");
            fetchUserCredits();
            onGeneratingChange?.(false);
            return;
          } else if (data.data.status === 'failed') {
            toast.error(`生成失败: ${data.data.error?.detail || '未知错误'}`);
            onGeneratingChange?.(false);
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // 5秒后再次轮询
        } else {
          toast.error("生成超时，请稍后查看结果");
        }
      } catch (error) {
        console.error('Polling error:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        }
      }
    };

    poll();
  };

  const handleModelSelect = (modelId: string) => {
    const model = models.find(m => m.model_id === modelId);
    if (model) {
      setSelectedModel(model);
    }
  };

  const getModelTypeIcon = (type: string) => {
    const Icon = MODEL_TYPE_ICONS[type as keyof typeof MODEL_TYPE_ICONS] || MessageSquare;
    return <Icon className="w-4 h-4" />;
  };

  const getModelTypeLabel = (type: string) => {
    return MODEL_TYPE_LABELS[type as keyof typeof MODEL_TYPE_LABELS] || type;
  };





  const renderModelOptions = () => {
    if (!selectedModel) return null;

    const { model_type, supported_features = [] } = selectedModel;

    return (
      <div className="space-y-4">
        {/* 文本生成选项 */}
        {model_type === 'text' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="max_tokens">最大输出长度</Label>
              <Input
                id="max_tokens"
                type="number"
                placeholder="1000"
                value={options.max_tokens || ''}
                onChange={(e) => setOptions({...options, max_tokens: parseInt(e.target.value) || 1000})}
              />
            </div>
            <div>
              <Label htmlFor="temperature">创造性 (0-1)</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="0.7"
                value={options.temperature || ''}
                onChange={(e) => setOptions({...options, temperature: parseFloat(e.target.value) || 0.7})}
              />
            </div>
          </div>
        )}

        {/* 图像生成选项 */}
        {model_type === 'image' && (
          <div className="grid grid-cols-2 gap-4">
            {supported_features.includes('variants') && (
              <div>
                <Label htmlFor="variants">生成数量</Label>
                <Select value={options.variants?.toString() || '1'} onValueChange={(value) => setOptions({...options, variants: parseInt(value)})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="z-[150]">
                    <SelectItem value="1">1张</SelectItem>
                    <SelectItem value="2">2张</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            
            {(supported_features.includes('aspectRatio') || supported_features.includes('size')) && (
              <div>
                <Label htmlFor="size">图像尺寸</Label>
                <Select value={options.size || options.aspectRatio || '1:1'} onValueChange={(value) => setOptions({...options, size: value, aspectRatio: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="z-[150]">
                    <SelectItem value="1:1">正方形 (1:1)</SelectItem>
                    <SelectItem value="16:9">横屏 (16:9)</SelectItem>
                    <SelectItem value="9:16">竖屏 (9:16)</SelectItem>
                    <SelectItem value="4:3">标准 (4:3)</SelectItem>
                    <SelectItem value="3:2">照片 (3:2)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        )}

        {/* 通用选项 */}
        <div>
          <Label htmlFor="cdn">CDN区域</Label>
          <Select value={options.cdn || 'global'} onValueChange={(value) => setOptions({...options, cdn: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="z-[150]">
              <SelectItem value="global">全球</SelectItem>
              <SelectItem value="zh">中国</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };



  return (
    <div className="space-y-6">
      {/* 统一的输入配置卡片 */}
      <Card className="bg-gradient-to-br from-card via-card to-accent/5">
        <CardHeader className="border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-primary to-accent">
              <Send className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              AI生成配置
            </span>
          </CardTitle>
          <CardDescription className="text-muted-foreground/80">
            选择AI模型并输入提示词，开始您的创意之旅
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          {/* 模型选择 */}
          <div>
            <Label htmlFor="model">AI模型</Label>
            {modelsLoading ? (
              <div className="flex items-center justify-center p-4 border rounded-md">
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                <span className="text-sm">加载模型列表...</span>
              </div>
            ) : modelsError ? (
              <div className="p-4 border rounded-md">
                <div className="text-red-600 text-sm">
                  加载失败: {modelsError}
                </div>
              </div>
            ) : (
              <Select value={selectedModel?.model_id || ""} onValueChange={handleModelSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="选择模型..." />
                </SelectTrigger>
                <SelectContent className="z-[150]">
                  {Object.entries(groupedModels).map(([type, typeModels]) => (
                    <div key={type}>
                      {!modelType && (
                        <div className="px-2 py-1.5 text-sm font-semibold text-gray-500 flex items-center gap-2">
                          {getModelTypeIcon(type)}
                          {getModelTypeLabel(type)}
                        </div>
                      )}
                      {typeModels.map((model) => (
                        <SelectItem key={model.model_id} value={model.model_id}>
                          <span className="font-medium">{model.model_name}</span>
                          <span className="text-xs text-muted-foreground ml-2">
                            {model.credits_per_unit} 积分/{model.unit_type}
                          </span>
                        </SelectItem>
                      ))}
                    </div>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* 选中模型的简洁说明 */}
          {selectedModel && (
            <div className="p-4 bg-gradient-to-r from-accent/10 to-primary/10 rounded-xl border border-border/30 backdrop-blur-sm">
              <div className="flex items-center gap-3 text-sm">
                <div className="p-2 rounded-lg bg-gradient-to-r from-primary to-accent">
                  <Coins className="w-4 h-4 text-primary-foreground" />
                </div>
                <div className="flex-1">
                  <span className="font-medium text-foreground">
                    {selectedModel.model_name}
                  </span>
                  <span className="text-muted-foreground ml-2">
                    {selectedModel.credits_per_unit} 积分/{selectedModel.unit_type}
                  </span>
                  {selectedModel.description && (
                    <div className="text-xs text-muted-foreground mt-1">{selectedModel.description}</div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div>
            <Label htmlFor="prompt">提示词</Label>
            <Textarea
              id="prompt"
              placeholder="请输入您的提示词..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={4}
            />
          </div>

          {renderModelOptions()}

          {/* 成本预估 */}
          {costEstimate && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                预估消耗: {costEstimate.cost_estimate.estimated_credits} 积分
                {!costEstimate.user_credits.can_afford && (
                  <span className="text-red-600 ml-2">
                    (积分不足，还需 {costEstimate.user_credits.shortfall} 积分)
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* 积分显示 - 紧贴生成按钮上方 */}
          <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30">
            <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500">
              <Coins className="w-4 h-4 text-white" />
            </div>
            <div>
              <span className="text-sm font-medium text-foreground">当前积分余额</span>
              <div className="text-lg font-bold bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">
                {userCredits}
              </div>
            </div>
          </div>

          <Button
            onClick={handleGenerate}
            disabled={loading || !selectedModel || !prompt.trim() || (costEstimate && !costEstimate.user_credits.can_afford)}
            className="w-full h-12 text-base font-semibold"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                <Send className="w-5 h-5 mr-2" />
                开始生成
              </>
            )}
          </Button>
        </CardContent>
      </Card>


    </div>
  );
}
