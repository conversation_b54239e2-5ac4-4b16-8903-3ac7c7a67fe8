// components/test/PricingModalTestButton.tsx
'use client';

import { Button } from "@/components/ui/button";
import { useModal } from "@/hooks/useModal";
import { useParams } from "next/navigation";

export default function PricingModalTestButton() {
  const { openPricingModalNew } = useModal();
  const params = useParams();
  const locale = (params?.locale as string) || 'en';

  const handleOpenPricingModal = () => {
    openPricingModalNew(locale, 'landing-page-test');
  };

  return (
    <div className="fixed bottom-4 right-4 z-40">
      <Button
        onClick={handleOpenPricingModal}
        className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg"
        size="lg"
      >
        🚀 测试定价模态框
      </Button>
    </div>
  );
}
