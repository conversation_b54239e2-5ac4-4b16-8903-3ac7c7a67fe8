// components/modals/PricingModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { getPricingPage } from "@/services/page";
import { Pricing as PricingType } from "@/types/blocks/pricing";
import PricingContent from "@/components/shared/PricingContent";

// 支持两种类型的 props
interface PricingModalNewProps {
  locale?: string;
  source?: string;
}

interface PricingModalOldProps {
  initialPlan?: 'free' | 'pro' | 'enterprise';
  source?: string;
}

type PricingModalProps = PricingModalNewProps | PricingModalOldProps;

export default function PricingModal(props: PricingModalProps) {
  // 检查是否是新版本的 props
  const isNewVersion = 'locale' in props;

  const locale = isNewVersion ? (props as PricingModalNewProps).locale || 'en' : 'en';
  const source = props.source || 'unknown';
  const t = useTranslations();
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  
  const [pricing, setPricing] = useState<PricingType | null>(null);
  const [isLoading, setIsLoading] = useState(isNewVersion);

  useEffect(() => {
    if (!isNewVersion) {
      // 如果是旧版本，不需要加载数据，直接显示简单的定价信息
      setIsLoading(false);
      return;
    }

    const loadPricingData = async () => {
      try {
        setIsLoading(true);
        const page = await getPricingPage(locale);
        if (page.pricing) {
          setPricing(page.pricing);
        }
      } catch (error) {
        console.error('Failed to load pricing data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPricingData();
  }, [locale, isNewVersion]);

  const content = (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : isNewVersion ? (
        // 新版本：使用从页面加载的真实数据
        pricing ? (
          <PricingContent pricing={pricing} isModal={true} />
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              page.
            </p>
          </div>
        )
      ) : (
        // 旧版本：显示简单的定价信息（向后兼容）
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            这是简化版的定价模态框。请使用 pricing-modal 类型获取完整的定价信息。
          </p>
        </div>
      )}
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>选择您的套餐</DialogTitle>
            <DialogDescription>
              选择最适合您需求的套餐，随时可以升级或降级
            </DialogDescription>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent className="max-h-[95vh]">
        <DrawerHeader className="text-left">
          <DrawerTitle>选择您的套餐</DrawerTitle>
          <DrawerDescription>
            选择最适合您需求的套餐，随时可以升级或降级
          </DrawerDescription>
        </DrawerHeader>
        <div className="px-4 overflow-y-auto flex-1">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              稍后再说
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
