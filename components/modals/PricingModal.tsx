// components/modals/PricingModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { getPricingPage } from "@/services/page";
import { Pricing as PricingType } from "@/types/blocks/pricing";
import PricingContent from "@/components/shared/PricingContent";

interface PricingModalProps {
  locale?: string;
  source?: string;
}

export default function PricingModal({
  locale = 'en',
  source = 'unknown'
}: PricingModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const [pricing, setPricing] = useState<PricingType | null>(null);
  const [modalTexts, setModalTexts] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 数据加载逻辑
  useEffect(() => {
    const loadPricingData = async () => {
      try {
        setIsLoading(true);
        const page = await getPricingPage(locale);
        if (page.pricing) {
          setPricing(page.pricing);
          setModalTexts(page.pricing.modal);
        }
      } catch (error) {
        console.error('Failed to load pricing data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPricingData();
  }, [locale]);

  const content = (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">{modalTexts?.loading || 'Loading...'}</span>
        </div>
      ) : pricing ? (
        <PricingContent pricing={pricing} isModal={true} />
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">{modalTexts?.error || 'Pricing information is temporarily unavailable'}</p>
        </div>
      )}
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('title')}</DialogTitle>
            <DialogDescription>{page.description}</DialogDescription>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent className="max-h-[95vh]">
        <DrawerHeader className="text-left">
          <DrawerTitle>{t('title')}</DrawerTitle>
          <DrawerDescription>{t('description')}</DrawerDescription>
        </DrawerHeader>
        <div className="px-4 overflow-y-auto flex-1">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              {page.cancel}
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
