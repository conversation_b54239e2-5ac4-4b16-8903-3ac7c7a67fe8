// components/modals/PricingModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { Check } from "lucide-react";

// 定义这个组件接收的 props 类型
interface PricingModalProps {
  initialPlan?: 'free' | 'pro' | 'enterprise';
  source?: string;
}

export default function PricingModal({ 
  initialPlan = 'free',
  source = 'unknown'
}: PricingModalProps) {
  const t = useTranslations();
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const plans = [
    {
      id: 'free',
      name: '免费版',
      price: '¥0',
      period: '/月',
      features: ['基础AI功能', '每日10次生成', '标准支持'],
      recommended: false,
    },
    {
      id: 'pro',
      name: '专业版',
      price: '¥99',
      period: '/月',
      features: ['所有AI功能', '无限次生成', '优先支持', '高级模型访问'],
      recommended: true,
    },
    {
      id: 'enterprise',
      name: '企业版',
      price: '¥299',
      period: '/月',
      features: ['所有专业版功能', '团队协作', '专属客服', 'API访问'],
      recommended: false,
    },
  ];

  const handleSelectPlan = (planId: string) => {
    console.log(`选择套餐: ${planId}, 来源: ${source}`);
    // 这里可以添加跳转到支付页面的逻辑
    closeModal();
  };

  const content = (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`relative rounded-lg border p-6 ${
              plan.recommended
                ? 'border-primary bg-primary/5'
                : 'border-border'
            } ${initialPlan === plan.id ? 'ring-2 ring-primary' : ''}`}
          >
            {plan.recommended && (
              <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                  推荐
                </span>
              </div>
            )}
            
            <div className="text-center">
              <h3 className="text-lg font-semibold">{plan.name}</h3>
              <div className="mt-2">
                <span className="text-3xl font-bold">{plan.price}</span>
                <span className="text-muted-foreground">{plan.period}</span>
              </div>
            </div>

            <ul className="mt-6 space-y-3">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>

            <Button
              className="w-full mt-6"
              variant={plan.recommended ? 'default' : 'outline'}
              onClick={() => handleSelectPlan(plan.id)}
            >
              {plan.id === 'free' ? '继续免费使用' : '选择此套餐'}
            </Button>
          </div>
        ))}
      </div>
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>选择您的套餐</DialogTitle>
            <DialogDescription>
              选择最适合您需求的套餐，随时可以升级或降级
            </DialogDescription>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader className="text-left">
          <DrawerTitle>选择您的套餐</DrawerTitle>
          <DrawerDescription>
            选择最适合您需求的套餐，随时可以升级或降级
          </DrawerDescription>
        </DrawerHeader>
        <div className="px-4 overflow-y-auto">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              稍后再说
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
