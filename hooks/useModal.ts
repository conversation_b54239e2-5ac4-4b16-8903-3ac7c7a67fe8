// hooks/useModal.ts
import { useModalStore, ModalType } from '@/stores/modalStore';

/**
 * 自定义 Hook 用于模态框操作
 * 提供便捷的模态框控制方法
 */
export function useModal() {
  const { activeModal, openModal, closeModal, isModalOpen } = useModalStore();

  return {
    // 当前激活的模态框信息
    activeModal,
    
    // 打开模态框的方法
    openModal,
    
    // 关闭模态框的方法
    closeModal,
    
    // 检查模态框是否打开
    isModalOpen,
    
    // 便捷方法：打开登录模态框
    openLoginModal: () => openModal('login', {}),
    
    // 便捷方法：打开定价模态框
    openPricingModal: (initialPlan?: 'free' | 'pro' | 'enterprise', source?: string) =>
      openModal('pricing', { initialPlan, source }),

    // 便捷方法：打开新的定价模态框（与页面内容同源）
    openPricingModalNew: (locale?: string, source?: string) =>
      openModal('pricing-modal', { locale, source }),
    
    // 便捷方法：打开反馈模态框
    openFeedbackModal: (socialLinks?: Array<{ name: string; url: string; icon?: string }>) => 
      openModal('feedback', { socialLinks }),
    
    // 便捷方法：打开AI模型模态框
    openAIModelModal: (modelType?: 'text' | 'image' | 'video' | 'audio', modelId?: string) => 
      openModal('aiModel', { modelType, modelId }),
    
    // 便捷方法：打开用户资料模态框
    openUserProfileModal: (userId?: string, tab?: 'profile' | 'settings' | 'billing') => 
      openModal('userProfile', { userId, tab }),
    
    // 便捷方法：打开设置模态框
    openSettingsModal: (section?: 'general' | 'appearance' | 'notifications' | 'privacy') => 
      openModal('settings', { section }),
  };
}

/**
 * 兼容性 Hook - 用于替代原有的 showSignModal
 * 让现有代码可以平滑迁移到新的模态框系统
 */
export function useSignModal() {
  const { openModal, closeModal, isModalOpen } = useModalStore();
  
  return {
    showSignModal: isModalOpen('login'),
    setShowSignModal: (show: boolean) => {
      if (show) {
        openModal('login', {});
      } else {
        closeModal();
      }
    },
    openSignModal: () => openModal('login', {}),
    closeSignModal: () => closeModal(),
  };
}

/**
 * 兼容性 Hook - 用于替代原有的 showFeedback
 */
export function useFeedbackModal() {
  const { openModal, closeModal, isModalOpen } = useModalStore();
  
  return {
    showFeedback: isModalOpen('feedback'),
    setShowFeedback: (show: boolean) => {
      if (show) {
        openModal('feedback', {});
      } else {
        closeModal();
      }
    },
    openFeedbackModal: (socialLinks?: Array<{ name: string; url: string; icon?: string }>) => 
      openModal('feedback', { socialLinks }),
    closeFeedbackModal: () => closeModal(),
  };
}
